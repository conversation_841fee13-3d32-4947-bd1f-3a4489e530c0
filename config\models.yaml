# Model configurations for the CLI chatbot
# Each model has its own configuration with optimized parameters

models:
  llama-3.2-3b-instruct:
    model_id: "meta-llama/Llama-3.2-3B-Instruct"
    display_name: "Llama 3.2 3B Instruct"
    description: "Meta's Llama 3.2 3B instruction-tuned model"
    device: "cuda"
    torch_dtype: "float16"
    generation_config:
      max_new_tokens: 512
      temperature: 0.7
      top_p: 0.9
      top_k: 50
      do_sample: true
      repetition_penalty: 1.1
      pad_token_id: 128001
    system_prompt: |
      You are a helpful AI assistant. Provide accurate, concise, and helpful responses.
      Be friendly and professional in your interactions.
    
  llama-3.2-1b-instruct:
    model_id: "meta-llama/Llama-3.2-1B-Instruct"
    display_name: "Llama 3.2 1B Instruct"
    description: "Meta's Llama 3.2 1B instruction-tuned model (lighter version)"
    device: "cuda"
    torch_dtype: "float16"
    generation_config:
      max_new_tokens: 512
      temperature: 0.7
      top_p: 0.9
      top_k: 50
      do_sample: true
      repetition_penalty: 1.1
      pad_token_id: 128001
    system_prompt: |
      You are a helpful AI assistant. Provide accurate, concise, and helpful responses.
      Be friendly and professional in your interactions.

  mistral-7b-instruct:
    model_id: "mistralai/Mistral-7B-Instruct-v0.3"
    display_name: "Mistral 7B Instruct"
    description: "Mistral's 7B instruction-tuned model"
    device: "cuda"
    torch_dtype: "float16"
    generation_config:
      max_new_tokens: 512
      temperature: 0.7
      top_p: 0.9
      top_k: 50
      do_sample: true
      repetition_penalty: 1.1
    system_prompt: |
      You are a helpful AI assistant. Provide accurate, concise, and helpful responses.
      Be friendly and professional in your interactions.

# Default model to use when starting the chatbot
default_model: "llama-3.2-3b-instruct"

# Global settings
settings:
  max_conversation_history: 10
  save_conversations: true
  conversation_dir: "conversations"
  cache_dir: "model_cache"
  log_level: "INFO"
